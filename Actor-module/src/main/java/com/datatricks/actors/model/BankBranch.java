package com.datatricks.actors.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotEmpty;
import lombok.*;

import java.util.Date;
import java.util.UUID;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(
        name = "bank_branches",
        indexes = {@Index(name = "interbank_code_index", columnList = "interbank_code")},
        uniqueConstraints = @UniqueConstraint(columnNames = {"interbank_code", "deleted_at"}))
public class BankBranch {

    @Id
    @GeneratedValue
    @JsonProperty("id")
    private Long id;

    @Column(name = "reference", nullable = false, unique = true)
    @JsonProperty("reference")
    @NotEmpty(message = "reference: reference cannot be empty")
    private UUID reference;

    @Column(name = "interbank_code", nullable = false)
    @JsonProperty("interbank_code")
    @NotEmpty(message = "interbank_code: interbank_code cannot be empty")
    private String interbankCode;

    @Column(name = "swift", nullable = false)
    @JsonProperty("swift")
    @NotEmpty(message = "interbank_code: interbank_code cannot be empty")
    private String swift;

    @Column(name = "domiciliation")
    @JsonProperty("domiciliation")
    private String domiciliation;

    @Column(name = "region")
    @JsonProperty("region")
    private String region;

    @Column(name = "address")
    @JsonProperty("address")
    private String address;

    @Column(name = "city")
    @JsonProperty("city")
    private String city;

    @Column(name = "postal_code")
    @JsonProperty("postal_code")
    private String postalCode;

    @Column(name = "country")
    @JsonProperty("country")
    private String country;

    @Column(name = "created_at")
    @JsonProperty("created_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

    @Column(name = "modified_at")
    @JsonProperty("modified_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date modifiedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date deletedAt;

    public BankBranch(UUID reference, @NotEmpty(message = "country_code: country_code cannot be empty") String countryCode,
                      @NotEmpty(message = "bank_id: bank_id cannot be empty") String bankId,
                      @NotEmpty(message = "bank_name: bank_name cannot be empty") String bankName,
                      @NotEmpty(message = "code_swift: code_swift cannot be empty") String codeSwift,
                      @NotEmpty(message = "code_branch: code_branch cannot be empty") String codeBranch,
                      @NotEmpty(message = "city: city cannot be empty") String city,
                      @NotEmpty(message = "postal_code: postal_code cannot be empty") String postalCode,
                      @NotEmpty(message = "address: address cannot be empty") String address, String secondAddress) {
        this.reference = reference;
        this.interbankCode = countryCode + bankId + codeBranch;
        this.swift = codeSwift;
        this.country = countryCode;
        this.address = address;
        this.city = city;
        this.postalCode = postalCode;
        this.domiciliation = secondAddress;
    }

    @PrePersist
    protected void onCreate() {
        createdAt = new Date();
    }

    @PreUpdate
    protected void onUpdate() {
        modifiedAt = new Date();
    }
}
