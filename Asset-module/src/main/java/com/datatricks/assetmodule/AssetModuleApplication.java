package com.datatricks.assetmodule;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.servers.Server;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
@OpenAPIDefinition(servers = {@Server(url = "/", description = "Default Server URL"), @Server(url = "/", description = "Asset Server URL")})
public class AssetModuleApplication {

        public static void main(String[] args) {
            SpringApplication.run(AssetModuleApplication.class, args);
        }
}
