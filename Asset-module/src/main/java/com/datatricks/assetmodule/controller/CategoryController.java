package com.datatricks.assetmodule.controller;

import com.datatricks.assetmodule.model.dto.CategoryDto;
import com.datatricks.assetmodule.model.dto.PageDto;
import com.datatricks.assetmodule.service.CategoryService;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.Explode;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.enums.ParameterStyle;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Map;

@RestController
@RequestMapping("/api")
@AllArgsConstructor
public class CategoryController {
    private final CategoryService categoryService;

    @Cacheable(value = "categories", key = "#params.toString()", unless = "#result.body.total == 0")
    @GetMapping("/v1/assets/categories")
    public ResponseEntity<PageDto<CategoryDto>> getCategories( @Parameter(name = "params",
            in = ParameterIn.QUERY,
            schema = @Schema(type = "object", implementation = CategoryDto.class),
            style = ParameterStyle.FORM,
            explode = Explode.TRUE)@RequestParam Map<String, String> params) {
        try {
            return categoryService.getCategories(params);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatusCode.valueOf(200)).body(PageDto.<CategoryDto>builder()
                    .data(new ArrayList<>())
                    .total(0).build());
        }
    }


}
