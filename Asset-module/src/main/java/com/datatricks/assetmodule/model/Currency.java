package com.datatricks.assetmodule.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.*;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "currencies")
public class Currency {
    @Id
    @JsonProperty("id")
    private Long id;

    @Column(name = "name")
    @JsonProperty("name")
    @NotBlank(message = "please provide a currency name")
    private String name;

    @Column(name = "code")
    @JsonProperty("code")
    @NotBlank(message = "please provide a currency code")
    private String code;

    @Column(name = "active")
    @JsonProperty("active")
    private Boolean active;

    public Currency(Long id) {
        this.id = id;
    }

    public Currency(String code, String label) {
        this.code = code;
        this.name = label;
    }

    public Currency(String code, String label, Boolean active) {
        this.code = code;
        this.name = label;
        this.active = active;
    }
}
