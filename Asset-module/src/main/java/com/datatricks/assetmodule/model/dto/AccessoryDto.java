package com.datatricks.assetmodule.model.dto;

import com.datatricks.assetmodule.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
public class AccessoryDto implements PageableDto {
    @JsonProperty("id")
    @Schema(description = "Id of the accessory", example = "1")
    private Long id;

    @JsonProperty("nature")
    @Schema(description = "Nature of the accessory", example = "Nature")
    private String nature;

    @JsonProperty("type_arrangement")
    @Schema(description = "Type arrangement of the accessory", example = "Type arrangement")
    private String typeArrangement;

    @JsonProperty("title")
    @Schema(description = "Title of the accessory", example = "Title")
    private String title;

    @JsonProperty("financing_amount")
    @Schema(description = "Financing amount of the accessory", example = "1000.0")
    private Double financingAmount;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "Start date of the accessory", example = "2021-01-01")
    private LocalDate startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    @Schema(description = "End date of the accessory", example = "2021-01-01")
    private LocalDate endDate;

    @JsonProperty("is_separate_billing")
    @Schema(description = "Is separate billing of the accessory", example = "true")
    private Boolean isSeparateBilling;

    @JsonProperty("is_suspended_billing")
    @Schema(description = "Is suspended billing of the accessory", example = "true")
    private Boolean isSuspendedBilling;

    @JsonProperty("autoExtension")
    @Schema(description = "Auto extension of the accessory", example = "true")
    private Boolean autoExtension;

    @JsonProperty("tax")
    @Schema(description = "Tax of the accessory", example = "Tax")
    private String tax;

	@JsonProperty("status")
    @Schema(description = "Status of the accessory", example = "Status")
	private String status;

    @JsonProperty("timetable_id")
    @Schema(description = "Timetable of the accessory", example = "1")
    private Long timetableId;

    @JsonProperty("contract_actor_id")
    @Schema(description = "Contract actor id of the accessory", example = "1")
    private Long contractActorId;
}
