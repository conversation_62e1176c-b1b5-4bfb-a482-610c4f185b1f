<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.5.xsd">

    <changeSet id="create-nap-table" author="datatricks">
        <createTable tableName="dt_nap">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints unique="true" nullable="false"/>
            </column>
            <column name="label" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="country_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addForeignKeyConstraint baseTableName="dt_nap" baseColumnNames="country_code"
                                 referencedTableName="dt_countries" referencedColumnNames="code"
                                 constraintName="fk_nap_country"/>
    </changeSet>
    <changeSet id="1742823281689-1" author="datatricks">
        <sql>
            INSERT INTO public.dt_nap (id, code, label, country_code)
            VALUES
            (1, '251123', 'Autres ossatures et éléments de structures, plaques, barres, profilés et similaires, en fer, acier ou aluminium', 'FR'),
            (2, '274010', 'Appareils d''éclairage', 'FR'),
            (3, '274030', 'Eclairages écologiques', 'FR'),
            (4, '275121', 'Materiels de cuisine', 'FR'),
            (5, '281320', 'Pompes a air et compresseurs', 'FR'),
            (6, '282511', 'Equipements frigorifiques industriels', 'FR'),
            (7, '284112', 'Centres d''usinage, machines-outils a fonctions multiples', 'FR'),
            (8, '289229', 'Tombereaux automoteurs', 'FR'),
            (9, '289914', 'Autres machines d''imprimerie (impression numerique)', 'FR'),
            (10, '292021', 'Conteneurs', 'FR'),
            (11, '900313', 'Oeuvres originales de peintres, graphistes et sculpteurs', 'FR'),
            (12, '281311', 'Pompes pour liquides', 'FR'),
            (13, '281323', 'Compresseurs frigorifiques', 'FR'),
            (14, '282512', 'Dispositifs de conditionnement de l''air', 'FR'),
            (15, '282943', 'Machines automatiques de vente de produits', 'FR'),
            (16, '162410', 'Autres emballages en bois', 'FR'),
            (17, '222313', 'Reservoirs en matieres plastiques', 'FR'),
            (18, '252911', 'Reservoirs et citernes metalliques', 'FR'),
            (19, '289315', 'Materiels de boulangerie', 'FR'),
            (20, '310111', 'Vitrines', 'FR'),
            (21, '289317', 'Machines et appareils divers pour l''industrie alimentaire', 'FR'),
            (22, '289411', 'Machines de filature, tissage et tricotage', 'FR'),
            (23, '289420', 'Autres machines pour l''industrie textile et la confection', 'FR'),
            (24, '323014', 'Materiels pour la gymnastique ou l''athletisme', 'FR'),
            (25, '351110', 'Autres énergies alternatives', 'FR'),
            (26, '282215', 'Chariot de manutention automoteurs', 'FR'),
            (27, '283021', 'Tracteurs agricoles et services associes Puissance inferieur ou egale 37 Kw', 'FR'),
            (28, '289240', 'Machines à trier, broyer, mélanger la terre, la pierre, les mine', 'FR'),
            (29, '291043', 'Tracteurs routiers[3]', 'FR'),
            (30, '291059', 'Vehicules utilitaires a usages speciaux[2]', 'FR'),
            (31, '309210', 'Bicyclettes et autres cycles, non motorisés', 'FR'),
            (32, '452030', 'Lavage et nettoyage de vehicules automobiles', 'FR'),
            (33, '282214', 'Tours de forage ; grues ; ponts roulants, chariots cavaliers', 'FR'),
            (34, '283022', 'Tracteurs agricoles et services associes Puissance entre 37 Kw et 59 Kw', 'FR'),
            (35, '283023', 'Tracteurs agricoles et services associes Puissance > 59 Kw', 'FR'),
            (36, '221114', 'Pneumatiques pour tracteurs,autres pneumatiques neufs', 'FR'),
            (37, '284910', 'Machines-outils pour le travail de la pierre, du bois et d''autres matériaux durs', 'FR'),
            (38, '329953', 'Matériels de démonstration', 'FR'),
            (39, '470083', 'Equipements photographiques, optiques et de précision, services d''opticien', 'FR'),
            (40, '264030', 'Appareils d''enregistrement ou de reproduction du son ou des images', 'FR'),
            (41, '265160', 'Autres instruments et appareils de mesure, de contrôle et d''essai', 'FR'),
            (42, '303000', 'Aéronefs et engins spatiaux', 'FR'),
            (43, '282921', 'Équipements de nettoyage, remplissage, emballage ou conditionnement de bouteilles ou autres récipients', 'FR'),
            (44, '289230', 'Autres matériels de travaux publics', 'FR'),
            (45, '289422', 'Machines à laver de type industriel ; machines pour le nettoyage à sec ; machines à sécher d''une capacité supérieure à 10 kg', 'FR'),
            (46, '289610', 'Machines pour le mélange ou le malaxage du caoutchouc ou des matières plastiques; machines pour la presse, l''extrusion, le soufflage, le thermoformage du caoutchouc ou des matières plastiques; matériel de moulage ou rechapage des pneumatiques', 'FR'),
            (47, '292023', 'Autres remorques et semi-remorques', 'FR'),
            (48, '279000', 'Materiels Electriques Divers', 'FR'),
            (49, '325050', 'Appareils medicaux divers', 'FR'),
            (50, '263020', 'Materiels de telephonie', 'FR'),
            (51, '264033', 'Caméscopes et autres appareils d''enregistrement et de reproduction vidéographiques', 'FR'),
            (52, '310110', 'Meubles de bureau et de magasin / Eléments de décoration', 'FR'),
            (53, '582921', 'Logiciels', 'FR'),
            (54, '282310', 'Caisses enregistreuses et machines assimilées avec logiciel', 'FR'),
            (55, '802010', 'Services de systèmes de sécurité', 'FR'),
            (56, '620129', 'Originaux de logiciels', 'FR'),
            (57, '262018', 'Unités effectuant deux ou plusieurs des fonctions suivantes : impression, scannage, copie, télécopie', 'FR'),
            (58, '265111', 'Autres matériels de navigation type GPS ou assimilés', 'FR'),
            (59, '262012', 'Terminaux point de vente, gab et machines similaires, pouvant être connectés à un ordinateur ou un réseau', 'FR'),
            (60, '262017', 'Moniteurs et projecteurs utilisés principalement dans un système informatique', 'FR'),
            (61, '263021', 'Postes téléphoniques d''usager fixes à combinés sans fil', 'FR'),
            (62, '264020', 'Récepteurs de télévision, combinés ou non à un récepteur de radio ou un appareil d''enregistrement ou de reproduction du son ou des images', 'FR'),
            (63, '265164', 'Compte-tours, taximètres ; compteurs de vitesse et tachymètres', 'FR'),
            (64, '631112', 'Traitement de données, hébergement et activités connexes', 'FR'),
            (65, '263022', 'Téléphones pour réseaux cellulaires et autres réseaux sans fil', 'FR'),
            (66, '266000', 'Equipements électro médicaux', 'FR'),
            (67, '266011', 'Materiel de radiologie industrielle ou médicale', 'FR'),
            (68, '266012', 'Exception : scanner / IRM', 'FR'),
            (69, '325011', 'Materiels dentaires', 'FR'),
            (70, '275122', 'Rasoirs, appareils à épiler et tondeuses, à moteur électrique incorporé', 'FR');
        </sql>
    </changeSet>
</databaseChangeLog>