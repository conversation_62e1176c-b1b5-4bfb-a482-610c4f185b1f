package com.datatricks.batchmodule.model.dto;

import java.util.Date;

import com.datatricks.batchmodule.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AddressDto implements PageableDto {

    @JsonProperty("id")
    private Long id;

    @JsonProperty("reference")
    private String reference;

    @JsonProperty("city")
    private String city;

    @JsonProperty("country")
    private String country;

    @JsonProperty("zip_code")
    private String zipCode;

    @JsonProperty("region")
    private String region;

    private String type;

    @JsonProperty("number")
    private String nbr;

    @JsonProperty("distribution")
    private String distribution;

    @JsonProperty("road_extension")
    private String roadExtension;

    @JsonProperty("EntranceBuilding")
    private String entranceBuilding;

    @JsonProperty("head_office")
    private Boolean headOffice;

    @JsonProperty("commune")
    private String commune;

    @JsonProperty("is_branch")
    private Boolean isBranch;

    @JsonProperty("is_billing")
    private Boolean isBilling;

    @JsonProperty("is_delivery")
    private Boolean isDelivery;

    @JsonProperty("summary")
    private String summary;

    @JsonProperty("road_type")
    private String roadType;

    @JsonProperty("start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date startDate;

    @JsonProperty("end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private Date endDate;

    @JsonProperty("physical_address")
    private PhysicalAddressDto physicalAddress;
}
