package com.datatricks.batchmodule.model.dto;

import com.datatricks.batchmodule.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.Date;

@Getter
@Setter
public class InstalmentDto {

    private float amount;

    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate dueDate;

    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_FORMAT)
    private LocalDate limitDate;

    private Date createdAt;

    public InstalmentDto(TimetablesSummaryViewSeparateBillingDto timetableSummary) {
        this.setCreatedAt(new Date());
        this.amount = timetableSummary.getRent() + timetableSummary.getTaxAmount();
        this.dueDate = timetableSummary.getDueDate();
        this.limitDate = timetableSummary.getEndDate();
    }
    public InstalmentDto(TimetablesSummaryViewNotSeparateBillingDto timetableSummary) {
        this.setCreatedAt(new Date());
        this.dueDate = timetableSummary.getDueDate();
        this.limitDate = timetableSummary.getEndDate();
    }
}
