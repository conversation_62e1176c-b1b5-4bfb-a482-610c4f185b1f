package com.datatricks.batchmodule.service;

import com.datatricks.batchmodule.model.BatchAnomaly;
import com.datatricks.batchmodule.model.BatchInstance;
import com.datatricks.batchmodule.repository.BatchAnomalyRepository;
import com.datatricks.batchmodule.repository.BatchInstanceRepository;
import jakarta.transaction.Transactional;
import org.springframework.stereotype.Service;

@Service
public class BatchAnomalyService {
    private final BatchAnomalyRepository batchAnomalyRepository;
    private final BatchInstanceRepository batchInstanceRepository;

    public BatchAnomalyService(
            BatchAnomalyRepository
                    batchAnomalyRepository,
            BatchInstanceRepository batchInstanceRepository
    ) {
        this.batchAnomalyRepository = batchAnomalyRepository;
        this.batchInstanceRepository = batchInstanceRepository;
    }

    @Transactional
    public BatchAnomaly getAnomaliesByReference(String reference) {
        return batchAnomalyRepository.findByBatchInstanceReference(reference);
    }

    @Transactional
    public void saveAnomaly(BatchAnomaly anomaly, String batchInstanceReference) {
        try {
            BatchAnomaly existingAnomaly = batchAnomalyRepository
                    .findByBatchInstanceReference(batchInstanceReference);

            BatchInstance batchInstance = batchInstanceRepository
                    .findByReferenceAndDeletedAtIsNull(batchInstanceReference)
                    .orElseThrow(() -> new RuntimeException("Batch instance not found"));

            if (existingAnomaly != null) {
                existingAnomaly.setErrorCode(anomaly.getErrorCode());
                existingAnomaly.setErrorMessage(anomaly.getErrorMessage());
                existingAnomaly.setDueDate(anomaly.getDueDate());
                existingAnomaly.setTimeTableItemId(anomaly.getTimeTableItemId());
                batchAnomalyRepository.save(existingAnomaly);
            } else {
                anomaly.setBatchInstance(batchInstance);
                var savedAnomaly = batchAnomalyRepository.save(anomaly);
                batchInstance.setBatchAnomaly(savedAnomaly);
                batchInstanceRepository.save(batchInstance);
            }
        } catch (Exception e) {
            System.out.println("An error occurred while saving anomaly: " + e.getMessage());
        }
    }
}
