package com.datatricks.contracts.controller;

import com.datatricks.contracts.exception.handler.InformativeMessage;
import com.datatricks.contracts.model.dto.*;
import com.datatricks.contracts.service.RetributionService;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.Explode;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.enums.ParameterStyle;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Map;

import static com.datatricks.contracts.utils.ContractUtils.handleException;

@RestController
@RequestMapping("/api")
public class RetributionController {

    private final RetributionService retributionService;

    @Autowired
    public RetributionController(RetributionService retributionService) {
        this.retributionService = retributionService;
    }

    @Caching(
            put = @CachePut(value = "retributions", key = "#result.body.data.id"),
            evict = @CacheEvict(value = "retributionsList", allEntries = true)
    )
    @PostMapping(value = "/v1/contract-actors/{contract_actor_id}/retributions")
    public ResponseEntity<SingleResultDto<RetributionResponseDto>> createRetribution(
            @PathVariable Long contract_actor_id, @RequestBody @Valid RetributionDto retribution) {
        try {
            return retributionService.createRetribution(contract_actor_id, retribution);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Caching(
            evict = {
                    @CacheEvict(value = "retributions", key = "#retribution_id"),
                    @CacheEvict(value = "retributionsList", allEntries = true)
            }
    )
    @PutMapping(value = "/v1/contract-actors/{contract_actor_id}/retributions/{retribution_id}")
    public ResponseEntity<SingleResultDto<RetributionResponseDto>> updateRetribution(
            @PathVariable Long contract_actor_id, @PathVariable Long retribution_id, @RequestBody @Valid RetributionDto retribution) {
        try {
            return retributionService.updateRetribution(contract_actor_id, retribution_id, retribution);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Cacheable(value = "retributions", key = "#contract_actor_id + ':' + #retribution_id")
    @GetMapping(value = "/v1/contract-actors/{contract_actor_id}/retributions/{retribution_id}")
    public ResponseEntity<SingleResultDto<RetributionResponseDto>> getRetribution(@PathVariable Long contract_actor_id, @PathVariable Long retribution_id) {
        return retributionService.getRetribution(contract_actor_id, retribution_id);
    }

    @Cacheable(value = "retributionsList", key = "#contract_id + ':' + #params.toString()")
    @GetMapping(value = "/v1/contracts/{contract_id}/retributions")
    public ResponseEntity<PageDto<RetributionResponseDto>> getRetributions(
            @PathVariable Long contract_id,
            @Parameter(name = "params",
                    in = ParameterIn.QUERY,
                    schema = @Schema(type = "object", implementation = RetributionParam.class),
                    style = ParameterStyle.FORM,
                    explode = Explode.TRUE)
            @RequestParam Map<String, String> params) {
        try {
            return retributionService.getRetributions(contract_id, params);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatusCode.valueOf(200)).body(PageDto.<RetributionResponseDto>builder()
                    .data(new ArrayList<>())
                    .total(0).build());
        }
    }

    @DeleteMapping(value = "/v1/contract-actors/{contract_actor_id}/retributions/{retribution_id}")
    public ResponseEntity<InformativeMessage> deleteRetribution(@PathVariable Long contract_actor_id, @PathVariable Long retribution_id) {
        return retributionService.deleteRetribution(contract_actor_id, retribution_id);
    }

    @Cacheable(value = "retributionsScheduleList", key = "#contract_id")
    @GetMapping(value = "/v1/contracts/{contract_id}/retributions/schedule")
    public ResponseEntity<PageDto<TimetableItemDto>> getAllRetributionsSchedule(@PathVariable Long contract_id) {
        try {
            return retributionService.getAllRetributionsSchedule(contract_id);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }
}
