package com.datatricks.contracts.exception.handler;

import com.datatricks.contracts.exception.ExternalApiException;
import com.datatricks.contracts.model.dto.BusinessErrorDto;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class ExternalApiExceptionHandler {

    @ExceptionHandler(ExternalApiException.class)
    public ResponseEntity<?> handleConstraintViolation(ExternalApiException ex) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        mapper.setConfig(mapper.getDeserializationConfig().withoutFeatures(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES));
        switch (ex.getErrorStatus()) {
            case HttpStatus.BAD_REQUEST -> {
                BusinessErrorDto errorDto = mapper.readValue(ex.getMessage(), BusinessErrorDto.class);
                errorDto.setCode("EXTERNAL_API_BAD_REQUEST");
                return new ResponseEntity<>(errorDto, HttpStatus.BAD_REQUEST);
            }
            case HttpStatus.NOT_FOUND -> {
                TechnicalError errorDto = mapper.readValue(ex.getMessage(), TechnicalError.class);
                errorDto.setCode("EXTERNAL_API_NOT_FOUND");
                return new ResponseEntity<>(errorDto, HttpStatus.NOT_FOUND);
            }
            case HttpStatus.CONFLICT -> {
                TechnicalError errorDto = mapper.readValue(ex.getMessage(), TechnicalError.class);
                errorDto.setCode("EXTERNAL_API_CONFLICT");
                return new ResponseEntity<>(errorDto, HttpStatus.CONFLICT);
            }
            default -> {
                TechnicalError errorDto = new TechnicalError("Internal_Server_Error");
                errorDto.getTechnicalDetail().setDetail(ex.getLocalizedMessage());
                errorDto.getTechnicalDetail().setSource("ASSET");
                return new ResponseEntity<>(errorDto, ex.getErrorStatus());
            }
        }
    }
}
