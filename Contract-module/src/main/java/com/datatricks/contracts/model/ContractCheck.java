package com.datatricks.contracts.model;

import com.datatricks.kafkacommondomain.model.CheckResult;
import com.datatricks.kafkacommondomain.model.PageableHashMap;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.vladmihalcea.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Type;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Entity
@Table(name = "contract_check")
public class ContractCheck extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @Type(JsonType.class)
    @Column(columnDefinition = "jsonb")
    private PageableHashMap<String, CheckResult> data;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "draft_id")
    @JsonIgnore
    private Draft draft;
}
