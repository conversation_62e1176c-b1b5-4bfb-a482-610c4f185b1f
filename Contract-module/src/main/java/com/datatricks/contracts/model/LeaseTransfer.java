package com.datatricks.contracts.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Entity
@Table(name = "lease_transfer")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class LeaseTransfer extends BaseEntity{
    @Id
    @GeneratedValue
    private Long id;

    @Enumerated(EnumType.STRING)
    @JsonProperty("transfer_type")
    @Column(name = "transfer_type")
    private TransferType transferType;

    @Column(name = "price")
    private Double price;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "management_mandate_reference", referencedColumnName = "reference")
    private ManagementMandate managementMandate;

    @JsonProperty("lessor_reference")
    @Column(name = "lessor_reference")
    private String lessorReference;

    @JsonProperty("lessor_name")
    @Column(name = "lessor_name")
    private String lessorName;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "contract_reference", referencedColumnName = "reference")
    private Contract contract;

    public LeaseTransfer(TransferType transferType, Double price, String lessorReference, String lessorName, Contract contract, ManagementMandate managementMandate) {
        this.transferType = transferType;
        this.price = price;
        this.lessorReference = lessorReference;
        this.lessorName = lessorName;
        this.contract = contract;
        this.managementMandate = managementMandate;
    }
    public LeaseTransfer(TransferType transferType, Double price, String lessorReference, String lessorName, Contract contract) {
        this.transferType = transferType;
        this.price = price;
        this.lessorReference = lessorReference;
        this.lessorName = lessorName;
        this.contract = contract;
    }
}
