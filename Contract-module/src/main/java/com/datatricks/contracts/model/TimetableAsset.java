package com.datatricks.contracts.model;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "TIMETABLE_ASSETS", uniqueConstraints = @UniqueConstraint(
        columnNames = {"contract_actor_asset_id", "timetable_id", "deleted_at"}))
public class TimetableAsset extends BaseEntity {
    @Id
    @Column(name = "id")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "contract_actor_asset_id")
    private ContractActorAsset contractActorAsset;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "timetable_id")
    private Timetable timetable;
}
