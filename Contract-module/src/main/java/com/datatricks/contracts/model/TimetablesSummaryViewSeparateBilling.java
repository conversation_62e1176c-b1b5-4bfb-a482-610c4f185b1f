package com.datatricks.contracts.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Immutable;

import java.time.Instant;
import java.time.LocalDate;

/**
 * Mapping for DB view
 */
@Getter
@Setter
@Entity
@Immutable
@Table(name = "TIMETABLES_SUMMARY_VIEW_SEPARATE_BILLING")
public class TimetablesSummaryViewSeparateBilling {
    @Id
    @NotNull
    @Column(name = "ID", nullable = false)
    private Long id;

    @Size(max = 255)
    @NotNull
    @Column(name = "COMPANY_REFERENCE", nullable = false)
    private String companyReference;

    @Size(max = 255)
    @NotNull
    @Column(name = "COMPANY_NAME", nullable = false)
    private String companyName;

    @Size(max = 255)
    @NotNull
    @Column(name = "ACTOR_REFERENCE", nullable = false)
    private String actorReference;

    @Size(max = 255)
    @NotNull
    @Column(name = "ACTOR_NAME", nullable = false)
    private String actorName;

    @NotNull
    @Column(name = "ACTOR_ID", nullable = false)
    private Long actorId;

    @Size(max = 255)
    @Column(name = "CONTRACT_REFERENCE")
    private String contractReference;

    @NotNull
    @Column(name = "CONTRACT_ID", nullable = false)
    private Long contractId;

    @NotNull
    @Column(name = "COMPANY_ID", nullable = false)
    private Long companyId;

    @Size(max = 255)
    @NotNull
    @Column(name = "CONTRACT_ACTIVITY_CODE", nullable = false)
    private String contractActivityCode;

    @Size(max = 255)
    @NotNull
    @Column(name = "CONTRACT_PRODUCT_CODE", nullable = false)
    private String contractProductCode;

    @Size(max = 255)
    @Column(name = "TIMETABLE_STATUS")
    private String timetableStatus;

    @Size(max = 50)
    @Column(name = "TIMETABLE_ITEM_STATUS", length = 50)
    private String timetableItemStatus;

    @Column(name = "DEPRECIATION")
    private Float depreciation;

    @Column(name = "DUE_DATE")
    private LocalDate dueDate;

    @Column(name = "END_DATE")
    private LocalDate endDate;

    @Column(name = "INTEREST")
    private Float interest;

    @Column(name = "NOMINAL_RATE")
    private Float nominalRate;

    @Column(name = "RATE")
    private Float rate;

    @Column(name = "RENT")
    private Float rent;

    @Column(name = "RESIDUAL_VALUE")
    private Float residualValue;

    @Column(name = "START_DATE")
    private LocalDate startDate;

    @Column(name = "TAX_AMOUNT")
    private Float taxAmount;

    @Column(name = "AMORTIZATION")
    private Float amortization;

    @Column(name = "UNPAID")
    private Float unpaid;

    @Column(name = "TIMETABLE_ID")
    private Long timetableId;

    @Size(max = 255)
    @Column(name = "ROLE_CODE")
    private String roleCode;

    @Size(max = 255)
    @Column(name = "IBAN")
    private String iban;

    @Column(name = "CLIENT_ADDRESS_ID")
    private Long clientAddressId;

    @Column(name = "IS_SEPARATE_BILLING")
    private boolean isSeparateBilling;

    @Size(max = 255)
    @Column(name = "line_type_code")
    private String lineTypeCode;

    @Size(max = 255)
    @Column(name = "TAX")
    private Double tax;

    @Size(max = 255)
    @Column(name = "TAX_CODE")
    private String taxCode;

    @Column(name = "DELETED_AT")
    private Instant deletedAt;

    @Column(name = "CREATED_AT")
    private Instant createdAt;

    @Column(name = "MODIFIED_AT")
    private Instant modifiedAt;

}