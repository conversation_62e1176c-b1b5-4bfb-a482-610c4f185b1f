package com.datatricks.invoices.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@Getter
@Setter
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "provider_expense_payments")
public class ProviderExpensePayments extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

    @Column(name = "type")
    @JsonProperty("type")
    private String type;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "payment_method_id")
    @JsonProperty("payment_method")
    @NotNull(message = "please provide an payment Method")
    private PaymentMethod method;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "bank_account_id")
    @JsonProperty("bank_account")
    private BankAccount bankAccount;

    @OneToOne
    @JoinColumn(name = "expense_id")
    private Expense expense;
}
