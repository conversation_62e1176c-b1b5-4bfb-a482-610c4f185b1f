package com.datatricks.invoices.repository;

import com.datatricks.invoices.model.OperationNatureTypeMapping;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface OperationNatureTypeMappingRepository extends JpaRepository<OperationNatureTypeMapping, Long> {
    void deleteByOperationNatureMappingIdAndTypeId(Long id, Long id1);

    Optional<OperationNatureTypeMapping> findByOperationNatureMappingIdAndTypeId(Long id, Long id1);

    Optional<OperationNatureTypeMapping> findByOperationNatureTypeMappingCode(String operationNatureTypeMappingCode);

    void deleteByOperationNatureTypeMappingCode(String operationNatureTypeMappingCode);
}
