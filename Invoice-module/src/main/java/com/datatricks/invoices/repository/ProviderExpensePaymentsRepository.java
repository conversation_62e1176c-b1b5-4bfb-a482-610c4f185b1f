package com.datatricks.invoices.repository;

import com.datatricks.invoices.model.ProviderExpensePayments;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.query.Param;

import java.util.Optional;


public interface ProviderExpensePaymentsRepository extends JpaRepository<ProviderExpensePayments, Long> {

    Optional<ProviderExpensePayments> findByExpenseIdAndIdAndDeletedAtIsNull(@Param("expense_id") Long expenseId, @Param("id") Long id);
    ProviderExpensePayments findByExpenseIdAndDeletedAtIsNull(@Param("expense_id") Long expenseId);
}
