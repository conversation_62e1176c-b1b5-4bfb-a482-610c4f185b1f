FROM public.ecr.aws/docker/library/node:18.20.4-alpine

# Install Infisical
RUN apk add --no-cache bash curl && curl -1sLf \
'https://dl.cloudsmith.io/public/infisical/infisical-cli/setup.alpine.sh' | bash \
&& apk add infisical

WORKDIR /var/www

COPY package.json .
RUN npm install --quiet

COPY . .

# Set execute permission for the script
RUN chmod +x infisical.sh

RUN npm install sequelize sequelize-cli pg

EXPOSE 8808

CMD ["npm", "run", "start-local"]
