# Database Export and Environment Sync Guide

This guide explains how to export your existing database schema and data to Liquibase changesets for deployment to other environments.

## 🎯 Overview

The database export solution provides tools to:
- **Export existing database schema** to Liquibase XML changesets
- **Export existing data** to Liquibase data changesets  
- **Compare databases** and generate diff changesets
- **Synchronize environments** with automated deployment packages
- **Validate synchronization** between environments

## 🚀 Quick Start

### Export Everything (Recommended)
```bash
# Export complete database (schema + data)
npm run db:export

# This creates:
# - db/changelog/generated/schema-export.xml
# - db/changelog/generated/data-export.xml  
# - db/changelog/generated/db.master-export.xml
```

### Create Deployment Package
```bash
# Create complete deployment package
npm run db:sync package

# This creates deployment-ready files in:
# - db/changelog/deployment/
```

## 📋 Available Commands

### Export Commands
```bash
npm run db:export              # Export schema + data
npm run db:export:schema       # Export only schema
npm run db:export:data         # Export only data
```

### Sync Commands  
```bash
npm run db:sync                # Sync environments
npm run db:sync package        # Create deployment package
```

### Diff Commands
```bash
npm run db:diff                # Generate diff changelog
npm run db:diff show           # Show differences only
npm run db:diff validate       # Validate synchronization
```

## 🔧 Environment Configuration

### Target Database Configuration
Set these environment variables to configure target database:

```bash
export TARGET_DB_HOST=your-target-host
export TARGET_DB_PORT=5432
export TARGET_DB_NAME=dt-static-tables-target
export TARGET_DB_USER=postgres
export TARGET_DB_PASSWORD=your-password
```

### Example .env file
```bash
# Source database (current)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=dt-static-tables
DB_USER=postgres
DB_PASSWORD=U6GjpKQpsrwjZI

# Target database (for comparison/sync)
TARGET_DB_HOST=production-host
TARGET_DB_PORT=5432
TARGET_DB_NAME=dt-static-tables-prod
TARGET_DB_USER=postgres
TARGET_DB_PASSWORD=prod-password
```

## 📁 Generated File Structure

After running export commands, you'll have:

```
Static-tables/
├── db/
│   └── changelog/
│       ├── generated/
│       │   ├── schema-export.xml          # Database schema
│       │   ├── data-export.xml            # Database data
│       │   ├── db.master-export.xml       # Master export file
│       │   └── diff-changelog.xml         # Diff between databases
│       └── deployment/
│           ├── db.deployment-changelog.xml # Deployment-ready changelog
│           ├── DEPLOYMENT_INSTRUCTIONS.md  # Deployment guide
│           └── package-info.json          # Package metadata
```

## 🚀 Deployment Scenarios

### Scenario 1: New Environment (Clean Database)

1. **Export from source:**
   ```bash
   npm run db:export
   ```

2. **Copy files to target environment:**
   ```bash
   # Copy entire db/changelog/ directory
   # Copy config/liquibase-config.js
   # Copy scripts/ directory
   ```

3. **Deploy to target:**
   ```bash
   # Update database config for target
   # Then run:
   npm run db:migrate
   ```

### Scenario 2: Existing Environment (Update Database)

1. **Generate diff:**
   ```bash
   npm run db:diff
   ```

2. **Review changes:**
   ```bash
   # Check db/changelog/generated/diff-changelog.xml
   # Review what will be changed
   ```

3. **Apply changes:**
   ```bash
   npm run db:migrate
   ```

4. **Validate:**
   ```bash
   npm run db:diff validate
   ```

### Scenario 3: Complete Environment Sync

1. **Create deployment package:**
   ```bash
   npm run db:sync package
   ```

2. **Deploy package:**
   ```bash
   # Follow instructions in:
   # db/changelog/deployment/DEPLOYMENT_INSTRUCTIONS.md
   ```

## 🔍 Validation and Troubleshooting

### Validate Export
```bash
npm run db:validate
```

### Check Migration Status
```bash
npm run db:status
```

### Show Database Differences
```bash
npm run db:diff show
```

### Validate Synchronization
```bash
npm run db:diff validate
```

## ⚠️ Important Notes

### Before Export
1. **Backup your database** before running any export operations
2. **Test in development** environment first
3. **Review generated changesets** before deploying

### Schema Considerations
- **Existing tables**: Export will include all existing tables
- **Constraints**: Foreign keys, indexes, and constraints are included
- **Sequences**: Auto-increment sequences are preserved
- **Data types**: PostgreSQL-specific types are maintained

### Data Considerations
- **Large datasets**: Consider data volume for export/import time
- **Sensitive data**: Review data before deploying to other environments
- **Referential integrity**: Data export maintains foreign key relationships

## 🛠️ Advanced Usage

### Custom Export Configuration
You can modify the export scripts to:
- **Filter specific tables**: Edit export scripts to include/exclude tables
- **Custom data filtering**: Add WHERE clauses for data export
- **Schema-only deployment**: Use schema export without data

### Multiple Environment Sync
```bash
# Export from production
TARGET_DB_HOST=prod-host npm run db:export

# Compare with staging  
TARGET_DB_HOST=staging-host npm run db:diff

# Sync staging with production
TARGET_DB_HOST=staging-host npm run db:sync
```

## 📞 Support

### Common Issues

1. **"Table already exists" errors:**
   ```bash
   # Mark existing changesets as executed
   npm run db:migrate -- --mark-as-executed
   ```

2. **Connection timeouts:**
   - Check database connectivity
   - Verify credentials
   - Ensure PostgreSQL driver is available

3. **Large data export:**
   - Consider exporting schema and data separately
   - Use data filtering for large tables

### Getting Help
- Check `LIQUIBASE_MIGRATION.md` for general Liquibase usage
- Review generated `DEPLOYMENT_INSTRUCTIONS.md` for specific deployment steps
- Use `--help` flag with any script for detailed usage information

## 🎉 Success!

After following this guide, you'll have:
- ✅ Complete database schema exported to Liquibase
- ✅ All data exported as Liquibase changesets
- ✅ Deployment-ready packages for other environments
- ✅ Tools to maintain synchronization between environments
