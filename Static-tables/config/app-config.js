const defaultConfig = {
  // Database connection config
  db: {
    // most of the cases this will be localhost
    // unless you are connecting to remote database
    host: process.env.DB_HOST || 'localhost',

    // specify the port for the database connection
    port: process.env.DB_PORT || 5432,

    // name of the database connecting to
    name: process.env.DB_NAME || 'dt-static-tables-original',

    // database username
    user: process.env.DB_USER || 'postgres',

    // password for above database user
    password: process.env.DB_PASSWORD || 'U6GjpKQpsrwjZI',

    // we are using Sequelize for connecting to database
    // Sequelize supports Mysql, SQlite, PostgreSQL and MSSQL
    // As applicable use< either of 'mysql'|'sqlite'|'postgres'|'mssql'
    dialect: process.env.DB_DIALECT || 'postgres',
  },

    // A unique key used for signing JWT token
    // Please replace below key with your own
    JWTKey: "NL(K(]`R6u%_hSg",

  // PORT to run our express app
  httpPort: 8807,

  limit: 5,

  offset: 0,

  SortBy: "createdAt",

  OrderBy: "DESC",

  language: "EN-US", // default seeders language  

  defaultReturnedLanguage: "FR", // default language for returned data  
};

module.exports = {
  ...defaultConfig,
  default: {
    username: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: process.env.DB_DIALECT,
    httpPort: process.env.SERVICE_PORT,
  },
};
