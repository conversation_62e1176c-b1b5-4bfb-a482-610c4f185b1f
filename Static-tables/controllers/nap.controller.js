/**
 * NAP Controller
 */
const { NapModel } = require("../db");
const NotFoundError = require("../error/exception/NotFound");
const ConflictError = require("../error/exception/Conflict");
const { sendSyncMessage } = require("../producer/Producer");
const OperationType = require("../models/Stream/operationType");
const { napController } = require("../utils/controller-factory");

async function update(req, res, next) {
    try {
        const code = req.params.code;
        const nap = await NapModel.findOne({
            where: { code }
        });

        if (!nap) {
            throw new NotFoundError("Nap not found", "Nap");
        }

        await nap.update(req.body);

        await sendSyncMessage(OperationType.PUT, "Nap", "Nap", await NapModel.findOne({
            where: { code }
        }));

        res.send({
            data: await NapModel.findOne({ where: { code } })
        });
    } catch (error) {
        next(error);
    }
}

async function remove(req, res, next) {
    try {
        const code = req.params.code;
        const nap = await NapModel.findOne({
            where: { code }
        });

        if (!nap) {
            throw new NotFoundError("Nap not found", "Nap");
        }
        if(nap.system_attribute) {
            throw new ConflictError("Cannot delete system attribute", "Nap");
        }

        await nap.destroy();
        await sendSyncMessage(OperationType.DELETE, "Nap", "Nap", nap);
        res.send({
            data: {
                message: `Nap with code ${code} has been deleted successfully`,
            },
        });
    } catch (error) {
        next(error);
    }
}

// Custom upload method preserved
async function upload(req, res, next) {
  try {
    const { NapModel } = require("../db");
    const { items } = req.body;

    await NapModel.bulkCreate(items, {
      validate: true,
    });

    res.send({
      data: {
        message: `Data has been created successfully`,
      },
    });

  } catch (err) {
    next(err);
  }
}

// Custom method: Get NAPs with their equipments
async function getNapsWithEquipments(req, res, next) {
    try {
        const { NapModel, EquipmentModel } = require("../db");
        const market_code = req.query.market_code;
        const country_code = req.query.country_code;

        const whereClause = {};
        if (country_code) {
            whereClause.country_code = country_code;
        }

        if(!market_code) return res.send({ data: [] });

        market_code.active = true;
        const result = await NapModel.findAll({
            attributes: {exclude: ["createdAt", "updatedAt"]},
            where: whereClause,
            include: [
                {
                    model: EquipmentModel,
                    where: { market_code },
                    as: "equipments",
                    attributes: {exclude: ["createdAt", "updatedAt"]},
                }
            ]
        });

        return res.send({ data: result });
    } catch (err) {
        next(err);
    }
}

module.exports = {
    // Standard CRUD methods using enhanced generic controller
    search: napController.search,
    getOne: napController.getOne,
    create: napController.create,

    // Custom methods preserved
    update,
    remove,
    upload,
    getNapsWithEquipments,
};