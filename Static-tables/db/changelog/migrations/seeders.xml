<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-1">
        <loadData file="db/changelog/data/activity.csv" tableName="activities">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="associated_to" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="active" type="boolean"/>
            <column name="system_attribute" type="boolean"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-2">
        <loadData file="db/changelog/data/activity_translations.csv" tableName="activity_translations">
            <column name="id" type="NUMERIC"/>
            <column name="activity_code" type="STRING"/>
            <column name="activity_associated_to" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="language_code" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-3">
        <loadData file="db/changelog/data/country.csv" tableName="countries">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="language_code" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="country_tax_code" type="STRING"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-4">
        <loadData file="db/changelog/data/country_translations.csv" tableName="country_translations">
            <column name="id" type="NUMERIC"/>
            <column name="country_code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="language_code" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-5">
        <loadData file="db/changelog/data/currency.csv" tableName="currencies">
            <column name="id" type="NUMERIC"/>
            <column name="label" type="STRING"/>
            <column name="code" type="STRING"/>
            <column name="symbol" type="STRING"/>
            <column name="language" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="default_currency" type="STRING"/>
            <column name="active" type="boolean"/>
            <column name="country_code" type="STRING"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-6">
        <loadData file="db/changelog/data/legal-category.csv" tableName="legal_categories">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="system_attribute" type="boolean"/>
            <column name="country_code" type="STRING"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-7">
        <loadData file="db/changelog/data/legal-category_translations.csv" tableName="legal_category_translations">
            id,legal_category_code,label,language_code,createdAt,updatedAt
            <column name="id" type="NUMERIC"/>
            <column name="legal_category_code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="language_code" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-8">
        <loadData file="db/changelog/data/nap.csv" tableName="naps">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="system_attribute" type="boolean"/>
            <column name="country_code" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-9">
        <loadData file="db/changelog/data/naf.csv" tableName="nafs">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="system_attribute" type="boolean"/>
            <column name="country_code" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-10">
        <loadData file="db/changelog/data/naf_translations.csv" tableName="naf_translations">
            <column name="id" type="NUMERIC"/>
            <column name="naf_code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="language_code" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-11">
        <loadData file="db/changelog/data/allocation.csv" tableName="allocations">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="allocation_category_code" type="STRING"/>
            <column name="operation_code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="country_code" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-12">
        <loadData file="db/changelog/data/allocation_translations.csv" tableName="allocation_translations">
            <column name="id" type="NUMERIC"/>
            <column name="allocation_code" type="STRING"/>
            <column name="allocation_category_code" type="STRING"/>
            <column name="operation_code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="language_code" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-13">
        <loadData file="db/changelog/data/delegation.csv" tableName="delegations">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="short_label" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-14">
        <loadData file="db/changelog/data/market.csv" tableName="markets">
            id,code,label,description,system_attribute,active,createdAt,updatedAt

            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="description" type="STRING"/>
            <column name="system_attribute" type="boolean"/>
            <column name="active" type="boolean"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-15">
        <loadData file="db/changelog/data/equipment.csv" tableName="equipments">
            <column name="id" type="NUMERIC"/>
            <column name="label" type="STRING"/>
            <column name="category_code" type="STRING"/>
            <column name="country_code" type="STRING"/>
            <column name="market_code" type="STRING"/>
            <column name="active" type="boolean"/>
            <column name="system_attribute" type="boolean"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="code" type="STRING"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-16">
        <loadData file="db/changelog/data/milestone.csv" tableName="milestones">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="active" type="boolean"/>
            <column name="system_attribute" type="boolean"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-17">
        <loadData file="db/changelog/data/milestone_translations.csv" tableName="milestone_translations">
            id,milestone_code,label,language_code,createdAt,updatedAt
            <column name="id" type="NUMERIC"/>
            <column name="milestone_code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="language_code" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-18">
        <loadData file="db/changelog/data/nature.csv" tableName="natures">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="system_attribute" type="boolean"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-19">
        <loadData file="db/changelog/data/nature_translations.csv" tableName="nature_translations">
            <column name="id" type="NUMERIC"/>
            <column name="nature_code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="language_code" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-20">
        <loadData file="db/changelog/data/auto-catalog.csv" tableName="auto_catalogs">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="active" type="boolean"/>
            <column name="system_attribute" type="boolean"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
        </loadData>
    </changeSet>
</databaseChangeLog>