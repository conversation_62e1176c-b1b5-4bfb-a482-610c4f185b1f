<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">    <changeSet author="HoussemMOUSSA (generated)" id="seeders-1">
        <loadData file="db/changelog/data/activity.csv" tableName="activities">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="associated_to" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE" />
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE" />
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE" />
            <column name="active" type="boolean"/>
            <column name="system_attribute" type="boolean"/>
        </loadData>
    </changeSet>    <changeSet author="HoussemMOUSSA (generated)" id="seeders-2">
        <loadData file="db/changelog/data/activity_translations.csv" tableName="activity_translations">
            <column name="id" type="NUMERIC"/>
            <column name="activity_code" type="STRING"/>
            <column name="activity_associated_to" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="language_code" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE" />
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE" />
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE" />
        </loadData>
    </changeSet>    <changeSet author="HoussemMOUSSA (generated)" id="seeders-3">
        <loadData file="db/changelog/data/country.csv" tableName="countries">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="language_code" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE" />
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE" />
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE" />
            <column name="country_tax_code" type="STRING"/>
        </loadData>
    </changeSet>    <changeSet author="HoussemMOUSSA (generated)" id="seeders-4">
        <loadData file="db/changelog/data/country_translations.csv" tableName="country_translations">
            <column name="id" type="NUMERIC"/>
            <column name="country_code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="language_code" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE" />
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE" />
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE" />
        </loadData>
    </changeSet>    <changeSet author="HoussemMOUSSA (generated)" id="seeders-5">
        <loadData file="db/changelog/data/currency.csv" tableName="currencies">
            <column name="id" type="NUMERIC"/>
            <column name="label" type="STRING"/>
            <column name="code" type="STRING"/>
            <column name="symbol" type="STRING"/>
            <column name="language" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE" />
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE" />
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE" />
            <column name="default_currency" type="STRING"/>
            <column name="active" type="boolean"/>
            <column name="country_code" type="STRING"/>
        </loadData>
    </changeSet>    <changeSet author="HoussemMOUSSA (generated)" id="seeders-6">
        <loadData file="db/changelog/data/legal-category.csv" tableName="legal_categories">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE" />
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE" />
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE" />
            <column name="system_attribute" type="boolean"/>
            <column name="country_code" type="STRING"/>
        </loadData>
    </changeSet>    <changeSet author="HoussemMOUSSA (generated)" id="seeders-7">
        <loadData file="db/changelog/data/legal-category_translations.csv" tableName="legal_category_translations">
            <column name="id" type="NUMERIC"/>
            <column name="legal_category_code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="language_code" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE" />
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE" />
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE" />
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-8">
        <loadData file="db/changelog/data/nap.csv" tableName="naps" separator=";">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="system_attribute" type="boolean"/>
            <column name="country_code" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-9">
        <loadData file="db/changelog/data/naf.csv" tableName="nafs">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="system_attribute" type="boolean"/>
            <column name="country_code" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-10">
        <loadData file="db/changelog/data/naf_translations.csv" tableName="naf_translations">
            <column name="id" type="NUMERIC"/>
            <column name="naf_code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="language_code" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-11">
        <loadData file="db/changelog/data/allocation.csv" tableName="allocations">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="allocation_category_code" type="STRING"/>
            <column name="operation_code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="country_code" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-12">
        <loadData file="db/changelog/data/allocation_translations.csv" tableName="allocation_translations">
            <column name="id" type="NUMERIC"/>
            <column name="allocation_code" type="STRING"/>
            <column name="allocation_category_code" type="STRING"/>
            <column name="operation_code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="language_code" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-13">
        <loadData file="db/changelog/data/delegation.csv" tableName="delegations">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="short_label" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-14">
        <loadData file="db/changelog/data/market.csv" tableName="markets">
            id,code,label,description,system_attribute,active,createdAt,updatedAt

            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="description" type="STRING"/>
            <column name="system_attribute" type="boolean"/>
            <column name="active" type="boolean"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-15">
        <loadData file="db/changelog/data/equipment.csv" tableName="equipments">
            <column name="id" type="NUMERIC"/>
            <column name="label" type="STRING"/>
            <column name="category_code" type="STRING"/>
            <column name="country_code" type="STRING"/>
            <column name="market_code" type="STRING"/>
            <column name="active" type="boolean"/>
            <column name="system_attribute" type="boolean"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="code" type="STRING"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-16">
        <loadData file="db/changelog/data/milestone.csv" tableName="milestones">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="active" type="boolean"/>
            <column name="system_attribute" type="boolean"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-17">
        <loadData file="db/changelog/data/milestone_translations.csv" tableName="milestone_translations">
            id,milestone_code,label,language_code,createdAt,updatedAt
            <column name="id" type="NUMERIC"/>
            <column name="milestone_code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="language_code" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-18">
        <loadData file="db/changelog/data/nature.csv" tableName="natures">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="system_attribute" type="boolean"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-19">
        <loadData file="db/changelog/data/nature_translations.csv" tableName="nature_translations">
            <column name="id" type="NUMERIC"/>
            <column name="nature_code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="language_code" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-20">
        <loadData file="db/changelog/data/auto-catalog.csv" tableName="auto_catalogs">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="active" type="boolean"/>
            <column name="system_attribute" type="boolean"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE"/>
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-23">
        <loadData file="db/changelog/data/static_roles.csv" tableName="static_roles">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="is_exclusive" type="boolean"/>
            <column name="is_client" type="boolean"/>
            <column name="associated_to" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE" />
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE" />
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-21">
        <loadData file="db/changelog/data/roles.csv" tableName="roles" >
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="active" type="boolean"/>
            <column name="system_attribute" type="boolean"/>
            <column name="static_role_code" type="STRING"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-22">
        <loadData file="db/changelog/data/role_translations.csv" tableName="role_translations">
            <column name="id" type="NUMERIC"/>
            <column name="role_code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="language_code" type="STRING"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-24">
        <loadData file="db/changelog/data/static_role_translations.csv" tableName="static_role_translations">
            <column name="id" type="NUMERIC"/>
            <column name="static_role_code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="language_code" type="STRING"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE" />
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE" />
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-27">
        <loadData file="db/changelog/data/taxes.csv" tableName="taxes">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="start_date" type="DATE" />
            <column name="end_date" type="DATE" />
            <column name="country" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="description" type="STRING"/>
            <column name="active" type="boolean"/>
            <column name="system_attribute" type="boolean"/>
            <column name="type" type="STRING"/>
        </loadData>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="seeders-25">
        <loadData file="db/changelog/data/tax_rates.csv" tableName="tax_rates">
            <column name="id" type="NUMERIC"/>
            <column name="tax_code" type="STRING"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="start_date" type="DATE" />
            <column name="end_date" type="DATE" />
            <column name="rate" type="NUMERIC"/>
            <column name="active" type="boolean"/>
            <column name="system_attribute" type="boolean"/>
            <column name="reference" type="STRING"/>
        </loadData>
    </changeSet>    <changeSet author="HoussemMOUSSA (generated)" id="seeders-26">
        <loadData file="db/changelog/data/tax_rate_translations.csv" tableName="tax_rate_translations">
            <column name="id" type="NUMERIC"/>
            <column name="tax_code" type="STRING"/>
            <column name="tax_rate_code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="start_date" type="DATE" />
            <column name="end_date" type="DATE" />
            <column name="rate" type="NUMERIC"/>
            <column name="language_code" type="STRING"/>
        </loadData>
    </changeSet>
        <changeSet author="HoussemMOUSSA (generated)" id="seeders-28">
        <loadData file="db/changelog/data/tax_translations.csv" tableName="tax_translations">
            <column name="id" type="NUMERIC"/>
            <column name="tax_code" type="STRING"/>
            <column name="start_date" type="DATE" />
            <column name="end_date" type="DATE" />
            <column name="country" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="language_code" type="STRING"/>
        </loadData>
    </changeSet>    <changeSet author="HoussemMOUSSA (generated)" id="seeders-29">
        <loadData file="db/changelog/data/types.csv" tableName="types">
            <column name="id" type="NUMERIC"/>
            <column name="code" type="STRING"/>
            <column name="label" type="STRING"/>
            <column name="system_attribute" type="boolean"/>
        </loadData>
    </changeSet>    <changeSet author="HoussemMOUSSA (generated)" id="seeders-30">
        <loadData file="db/changelog/data/type_translations.csv" tableName="type_translations">
            <column name="id" type="NUMERIC"/>
            <column name="label" type="STRING"/>
            <column name="language_code" type="STRING"/>
            <column name="type_code" type="STRING"/>
        </loadData>
    </changeSet>    <changeSet author="HoussemMOUSSA (generated)" id="seeders-31">
        <loadData file="db/changelog/data/single_autos.csv" tableName="single_autos">
            <column name="id" type="NUMERIC"/>
            <column name="brand_code" type="STRING"/>
            <column name="brand_label" type="STRING"/>
            <column name="model_group_code" type="STRING"/>
            <column name="model_group_label" type="STRING"/>
            <column name="body_code" type="STRING"/>
            <column name="body_label" type="STRING"/>
            <column name="model_code" type="STRING"/>
            <column name="model_label" type="STRING"/>
            <column name="doors_number" type="STRING"/>
            <column name="version_code" type="STRING"/>
            <column name="version_label" type="STRING"/>
            <column name="variant_code" type="STRING"/>
            <column name="variant_label" type="STRING"/>
            <column name="color_code" type="STRING"/>
            <column name="color_label" type="STRING"/>
            <column name="interior_code" type="STRING"/>
            <column name="interior_label" type="STRING"/>
            <column name="marketing_flag" type="STRING"/>
            <column name="energy_code" type="STRING"/>
            <column name="energy_label" type="STRING"/>
            <column name="type_code" type="STRING"/>
            <column name="type_label" type="STRING"/>
            <column name="power" type="STRING"/>
            <column name="class_code" type="STRING"/>
            <column name="class_label" type="STRING"/>
            <column name="vds_vehicle" type="STRING"/>
            <column name="public_price_incl_tax" type="NUMERIC"/>
            <column name="painting_payment_code" type="STRING"/>
            <column name="status" type="boolean"/>
            <column name="equipment" type="STRING"/>
            <column name="is_new" type="boolean"/>
            <column name="created_at" type="TIMESTAMP WITH TIMEZONE" />
            <column name="modified_at" type="TIMESTAMP WITH TIMEZONE" />
            <column name="reference" type="STRING"/>
        </loadData>
    </changeSet>
</databaseChangeLog>