const redisClient = require("../config/redisClient");

const CACHE_PREFIX = "ms-statictables:";
const CACHE_TTL = 3600;

async function cacheMiddleware(req, res, next) {
    // Skip cache middleware if Redis client is not ready
    console.log("rdc",redisClient.status);
    
    if (redisClient.status !== "ready") {
        return next();
    }

    const cacheKey = `${CACHE_PREFIX}${req.url}`;
console.log("cache key",cacheKey)
    // On DELETE/POST/PUT/PATCH requests, delete the cache to prevent stale data
    if (req.method !== "GET") {
        try {
            // Delete the cache list of the requested static table resource
            await redisClient.del(`${CACHE_PREFIX}${req.originalUrl.split("/", 5).join("/")}`);

            if (req.method !== "POST") {
                // Delete the requested resource from the cache
                await redisClient.del(cacheKey);
            }
        } catch (err) {
            console.error("Error deleting cache:", err);
        }
        return next();
    }

        let sendCalled = false;
        try {
            const cachedResponse = await redisClient.get(cacheKey);
            console.log("cr",cachedResponse)
            if (cachedResponse) {
                // Cache hit: return the cached response
                console.log(`Cache hit for ${cacheKey}`);
                return res.send(JSON.parse(cachedResponse));
            } else {
                // Cache miss: proceed to fetch data and cache the response
                console.log(`Cache miss for ${cacheKey}`);
                const originalSend = res.send;
                res.send = function (body) {
                    if (!sendCalled) {
                        sendCalled = true;
                        // Cache the response
                        redisClient
                            .set(cacheKey, JSON.stringify(body), "EX", CACHE_TTL)
                            .catch((err) => {
                                console.error("Error caching response:", err);
                            });
                    }
                    originalSend.call(this, body);
                };
            }
        } catch (error) {
            console.error("Error accessing cache:", error);
            // Proceed without caching in case of error
        }

    // Move to the next middleware
    next();
}

module.exports = cacheMiddleware;
