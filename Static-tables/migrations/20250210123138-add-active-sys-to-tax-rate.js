"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
    async up (queryInterface, Sequelize) {
        await queryInterface.addColumn("tax_rates", "active", {
            type: Sequelize.BOOLEAN,
        });

        await queryInterface.addColumn("tax_rates", "system_attribute", {
            type: Sequelize.BOOLEAN,
        });
        await queryInterface.addColumn("tax_rates", "creationDate", {
            type: Sequelize.DATE,
            defaultValue: Sequelize.NOW,
        });
        await queryInterface.addConstraint("tax_rates", {
            fields: ["tax_code", "rate"],
            type: "unique",
            name: "unique_tax_code_rate_constraint"
        });
    },

    async down (queryInterface) {
        await queryInterface.removeColumn("tax_rates", "active");
        await queryInterface.removeColumn("tax_rates", "system_attribute");
        await queryInterface.removeColumn("tax_rates", "creationDate");
        await queryInterface.removeConstraint("tax_rates", "unique_tax_code_rate_constraint");
    }
};