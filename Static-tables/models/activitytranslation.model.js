module.exports = (sequelize, type) => {
    return sequelize.define(
        "activity_translations",
        {
            id: {
                type: type.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            activity_code: {
                type: type.STRING,
                allowNull: false,
                references: {
                    model: "activity",
                    key: "code"
                }
            },
            activity_associated_to: {
                type: type.STRING,
                allowNull: false
            },
            language_code: {
                type: type.STRING,
                allowNull: false
            },
            label: {
                type: type.STRING,
                allowNull: false
            }
        },
        {
            timestamps: true,
        },
    );

}
