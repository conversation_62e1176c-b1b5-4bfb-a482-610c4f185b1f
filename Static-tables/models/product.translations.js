const { DataTypes } = require("sequelize");
module.exports = (Sequelize) => {
    return Sequelize.define(
        "product_translations",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            product_code: {
                type: DataTypes.STRING,
                allowNull: false,
                references: {
                    model: "products",
                    key: "code"
                },
                onDelete: "CASCADE"
            },
            label: {
                type: DataTypes.STRING,
            },
            language_code: {
                type: DataTypes.STRING,
                allowNull: false
            },
        },
        {
            timestamps: true,
        },
    );

}
