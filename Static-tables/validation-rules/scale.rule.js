const BaseJoi = require("joi");
const Extension = require("joi-date-extensions");
const Joi = BaseJoi.extend(Extension);

module.exports = {
    create: {
        body: Joi.object().keys({
            code: Joi.string().min(2).max(255).optional().label("code"),
            label: Joi.string().min(2).max(255).required().label("label"),
            description: Joi.string().max(500).optional().allow(null).allow("").label("description"),
            condition: Joi.string().max(500).optional().label("condition"),
            start_date: Joi.date().iso().optional().allow(null).label("start_date"),
            end_date: Joi.date().iso().optional().allow(null).label("end_date"),
            country_code: Joi.string().min(2).max(255).optional().allow(null).allow("").label("country_code"),
            market_code: Joi.string().min(2).max(255).optional().allow(null).allow("").label("market_code"),
            currency_code: Joi.string().min(2).max(255).required().label("currency_code"),
            maximum_interest_rate: Joi.number().optional().label("maximum_interest_rate"),
            minimum_interest_rate: Joi.number().optional().label("minimum_interest_rate"),
            nominal_interest_rate: Joi.number().optional().label("nominal_interest_rate"),
            maximum_security_deposit: Joi.number().optional().label("maximum_security_deposit"),
            minimum_security_deposit: Joi.number().optional().label("minimum_security_deposit"),
            maximum_personal_contribution: Joi.number().optional().label("maximum_personal_contribution"),
            minimum_personal_contribution: Joi.when('has_personal_contribution', {
                is: Joi.boolean().valid(true),
                then: Joi.number().optional().min(100).label("minimum_personal_contribution"),
                otherwise: Joi.number().optional().label("minimum_personal_contribution")
            }),
            maximum_residual_value: Joi.number().optional().label("maximum_residual_value"),
            minimum_residual_value: Joi.number().optional().label("minimum_residual_value"),
            maximum_financing_duration: Joi.number().integer().optional().label("maximum_financing_duration"),
            minimum_financing_duration: Joi.number().integer().optional().label("minimum_financing_duration"),
            maximum_eligible_amount: Joi.number().optional().label("maximum_eligible_amount"),
            minimum_eligible_amount: Joi.number().optional().label("minimum_eligible_amount"),
            has_security_deposit: Joi.boolean().optional().label("has_security_deposit"),
            has_grace_period: Joi.boolean().optional().label("has_grace_period"),
            has_interest_payment: Joi.boolean().optional().label("has_interest_payment"),
            has_spread_calculation: Joi.boolean().optional().label("has_spread_calculation"),
            grace_period_duration: Joi.number().integer().optional().label("grace_period_duration"),
            with_interest_payment: Joi.boolean().optional().label("with_interest_payment"),
            has_personal_contribution: Joi.boolean().optional().label("has_personal_contribution"),
            financial_scoring: Joi.number().min(0).max(10).optional().label("financial_scoring"),
            asset_usage: Joi.string().optional().label("asset_usage"),
            rate_period: Joi.string().optional().label("rate_period"),
            nature: Joi.string().valid("MT", "VH").required().label("nature"),
            products: Joi.array().items(Joi.string()).optional().label("products"),
            businesses: Joi.array().items(Joi.string()).optional().label("businesses"),
            third_parties: Joi.array().items(
                Joi.object().keys({
                    actor_reference: Joi.string().required().label("actor_reference"),
                    role_code: Joi.string().required().label("role_code"),
                })
            ).optional().label("third_parties"),
            scale_service_list: Joi.array().items(Joi.string()).optional().label("scale_service_list"),
            assets: Joi.array().items(Joi.string()).optional().label("assets"),
            material_designation: Joi.string().optional().label("material_designation"),
            minimum_mileage: Joi.number().optional().label("minimum_mileage"),
            maximum_mileage: Joi.number().optional().label("maximum_mileage"),
            channel_of_acquisition: Joi.string().optional().label("channel_of_acquisition"),
            customer_type: Joi.string().optional().label("customer_type"),
        }).options({ abortEarly: false }),
    },
    patch: Joi.object().keys({
        status: Joi.string().valid("INI", "OPERATED", "SUSPENDED", "EXPIRED").required().label("status")
    })
};